import 'package:flutter/cupertino.dart';

import '../pages/home_page.dart';
import '../pages/login_Page.dart';

class NavigatorUtil {
  static BuildContext? _context;

  //用在没有context的地方,需要在homepage赋值
  static updateContext(BuildContext context) {
    NavigatorUtil._context = context;
    print('ini:$_context');
  }

  //跳转--->指定页面
  static void push(BuildContext context, Widget page) {
    Navigator.push(context, CupertinoPageRoute(builder: (context) => page));
  }

  //跳转首页
  static void goHomePage(BuildContext context) {
    Navigator.pushReplacement(
      context,
      CupertinoPageRoute(builder: (context) => HomePage()),
    );
  }

  //跳转到登陆页
  static void goLoginPage() {
    Navigator.pushReplacement(
      _context!,
      CupertinoPageRoute(builder: (context) => LoginPage()),
    );
  }
}
