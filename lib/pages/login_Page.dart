import 'package:flutter/material.dart';
import 'package:trip_flutter/util/view_util.dart';
import 'package:trip_flutter/widget/input_widget.dart';
import 'package:url_launcher/url_launcher.dart';

import '../dao/login_dao.dart';
import '../util/String_util.dart';
import '../widget/login_widget.dart';

//login page
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  bool loginenable = false;
  String? username;
  String? password;

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Stack(children: [...background(), _Content()]));

    ///...表示返回一个集合
  }

  background() {
    return [
      Positioned.fill(
        child: Image.asset('assets/images/img1.jpg', fit: BoxFit.cover),
      ),

      ///BoxFit 的正确定义
      // BoxFit 是一个枚举类型（enum），而不是函数或 Widget 容器。它是 Flutter 框架中定义的一个枚举，用于指定如何将一个部件调整大小以适应其容器。
      // lib/pages
      // 在这个例子中：
      // fit 是 Image 类的一个参数
      // BoxFit.cover 是传递给这个参数的枚举值
      // 为什么是枚举而不是其他
      // 枚举的优势：
      // 提供有限的、预定义的选项集合
      // 代码更清晰，避免使用魔法字符串
      // 编译时类型检查，避免错误
      // 不是函数：
      // BoxFit.cover 不执行任何操作，只是一个值
      // 实际的缩放逻辑在渲染引擎中实现
      // 不是 Widget：
      // BoxFit 只是一个配置值
      // 实际的布局和渲染由接收这个值的 Widget（如 Image）处理
      Positioned.fill(
        child: Container(decoration: BoxDecoration(color: Colors.black54)),
      ),
    ];
  }
  //[]也就是返回的集合

  _Content() {
    return Positioned.fill(
      left: 25,
      right: 25,
      child: ListView(
        children: [
          hiSpace(heightDouble: 100),
          const Text(
            '用户名密码登陆',
            style: TextStyle(fontSize: 26, color: Colors.white),
          ),
          hiSpace(heightDouble: 40),
          InputWidget(
            '输入用户名',
            onChanged: (text) {
              username = text;
              _checkInput();
            },
            //这段代码定义了一个匿名函数（也称为 lambda 函数或闭包），作为  onChanged 参数的值。当用户在输入框中输入或修改文本时，TextField 会调用  onChanged 回调.输入框的当前文本值会作为参数传递给这个匿名函数
          ),
          hiSpace(heightDouble: 10),
          InputWidget(
            '请输入密码',
            obscureText: true,
            onChanged: (text) {
              password = text;
              _checkInput();
            },
          ),
          hiSpace(heightDouble: 45),
          LoginButton(
            title: '登陆',
            enable: loginenable,
            onPressed: () => _login(),
          ),
          hiSpace(heightDouble: 10),
          Align(
            alignment: Alignment.centerRight,
            child: InkWell(
              onTap: () => _jumpRegisterPage(),
              child: Text(
                '注册账号',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _checkInput() {
    bool enable;
    if (isNotEmpty(username) && isNotEmpty(password)) {
      enable = true;
    } else {
      enable = false;
    }
    setState(() {
      loginenable = enable;
      // print(loginenable);
    });
  }

  void _login() async{
    try{
      var result = await LoginDAO.login(username: username!, password: password!);
      print('登陆成功');
    }catch(e){
      print(e);
    }
  }

  void _jumpRegisterPage() async {
    Uri uri = Uri.parse(
      "https://api.devio.org/uapi/swagger-ui.html#/Account/registrationUsingPOST",
    );
    if (! await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      //指定在外部应用（如浏览器）中打开 URL
      //launchUrl 返回一个 Future<bool>，表示是否成功打开 URL
      // ! 操作符取反，所以条件变成"如果打开 URL 失败"
      throw 'Could not launch $uri';
    }
  }
}
