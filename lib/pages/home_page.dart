import 'package:flutter/material.dart';
import 'package:trip_flutter/dao/home_dao.dart';
import 'package:trip_flutter/dao/login_dao.dart';
import 'package:trip_flutter/model/home_model.dart';
import 'package:trip_flutter/util/navigator_util.dart';

import '../widget/banner_widget.dart';

class HomePage extends StatefulWidget {
  static Config? configModel;
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with AutomaticKeepAliveClientMixin {
  double appBarAlpha = 0;
  double appBarScrollOffset = 100;

  //HomeModel里面封装的数据模型
  List<CommonModel> localNavList = [];
  List<CommonModel> bannerList = [];
  List<CommonModel> subNavList = [];
  GridNav? gridNavModel;
  SalesBox? salesBoxModel;


  @override
  void initState() {
    super.initState();
    _handleRefresh();
  }

  get _logoutBTN => TextButton(
    onPressed: () {
      LoginDAO.logout();
    },
    child: Text('退出'),
  );

  get _appBar => Opacity(
    opacity: appBarAlpha,
    child: Container(
      height: 90,
      decoration: const BoxDecoration(color: Colors.white),
      child: const Center(
        child: Padding(padding: EdgeInsets.only(top: 40), child: Text('首页')),
      ),
    ),
  );

  get _listView => ListView(
    children: [
      BannerWidget(bannerList: bannerList),
      _logoutBTN,
      Text(bodyString),
      SizedBox(height: 800, child: ListTile(title: Text('hahahaha'))),
    ],
  );



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: //监听列表滚动
            NotificationListener(
              child: _listView,
              onNotification: (scrollNotification) {
                if (scrollNotification is ScrollUpdateNotification &&
                    scrollNotification.depth == 0) {
                  setState(() {
                    _onScroll(scrollNotification.metrics.pixels);
                    // appBarAlpha = scrollNotification.metrics.pixels / 100;
                  });
                }
                return false;
              },
            ),
          ),
          _appBar,
        ],
      ),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  void _onScroll(offset) {
    appBarAlpha = offset / appBarScrollOffset;

    //这个边界思想非常重要！！！
    if (appBarAlpha < 0) {
      appBarAlpha = 0;
    } else if (appBarAlpha > 1) {
      appBarAlpha = 1;
    }
  }

  Future<void> _handleRefresh() async {
    try {
       HomeModel? model = await HomeDAO.fetch();
      setState(() {
        HomePage.configModel = model?.config;
        localNavList = model.localNavList;
        subNavList = model.subNavList;
        gridNavModel = model.gridNav;
        salesBoxModel = model.salesBox;
        bannerList = model.bannerList ?? [];

      });
    } catch (err) {
      debugPrint(err.toString());
    }
  }
}
