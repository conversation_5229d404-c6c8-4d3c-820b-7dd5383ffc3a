import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:trip_flutter/dao/login_dao.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  get _logoutBTN => ElevatedButton(onPressed: () {
    LoginDAO.logout();
  }, child: Text('退出'));

  @override
  Widget build(BuildContext context) {
    return AppBar(title: Text('首页'),actions: [_logoutBTN],);
  }
  }
}
