///登陆接口

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'header_util.dart';

class LoginDAO {
  static const boardingpass = 'boardingpass';

  static Future<void> login({required String username, required String password}) async {
    Map<String, String> paramsMap = {};
    paramsMap['username'] = username;
    paramsMap['password'] = password;
    var uri = Uri.https('api.devie.org', '/waki/user/login', paramsMap);
    
    // 异步获取 headers
    final headers = await hiHeaders();
    final response = await http.post(uri, headers: headers);
    
    Utf8Decoder utf8decoder = const Utf8Decoder(); //fix chinese
    String bodyString = utf8decoder.convert(response.bodyBytes);
    print(bodyString);
    if (response.statusCode == 200) {
      var result = json.decode(bodyString);
      if (result['code'] == 0 && result['data'] != null) {
        //保存登陆令牌
        _saveBoardingpass(result['data']);
      } else {
        throw Exception(bodyString);
      }
    } else {
      throw Exception(bodyString);
    }
  }

  static void _saveBoardingpass(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString(boardingpass, value);
  }

  static Future<String?> getBoardingpass() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(boardingpass);
  }

  static void logout() async{
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove(boardingpass);
  }
} 
