import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:trip_flutter/model/home_model.dart';
import '../util/navigator_util.dart';
import 'header_util.dart';

class HomeDAO {
  static Future<HomeModel?> fetch() async {
    var uri = Uri.parse('https://api.geekailab.com/uapi/ft/home');
    final response = await http.get(uri, headers: await hiHeaders());
    Utf8Decoder uft8decoder = const Utf8Decoder(); //fix chinese
    String bodyString = uft8decoder.convert(response.bodyBytes);
    debugPrint(bodyString);
    if (response.statusCode == 200) {
      var result= jsonDecode(bodyString);
      return HomeModel.fromJson(result['data']);
      //替换了HomeModel后
    } else {
      if (response.statusCode == 401) {
        NavigatorUtil.goLoginPage();
      }
      throw Exception('Failed to load home data');
    }
  }
}
